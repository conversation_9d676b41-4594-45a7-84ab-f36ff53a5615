package com.example.addon.modules;

import com.example.addon.BaseModule;
import com.example.addon.utils.BlockUtilGrim;
import com.example.addon.utils.WorldUtils;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.world.CardinalDirection;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.*;
import net.minecraft.client.MinecraftClient;
import net.minecraft.item.Items;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.IntStream;

public class TreeAura extends BaseModule {

    // 设置组，用于组织模块的设置选项
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    // 种植树木的延迟设置（tick），控制种植操作之间的间隔时间
    private final Setting<Integer> plantDelay = sgGeneral.add(new IntSetting.Builder()
        .name("种植延迟").description("种植树木之间的延迟").defaultValue(6).min(0).sliderMax(25).build());
    // 施加骨粉的延迟设置（tick），控制对树苗使用骨粉的间隔时间
    private final Setting<Integer> bonemealDelay = sgGeneral.add(new IntSetting.Builder()
        .name("骨粉延迟").description("在树木上放置骨粉之间的延迟").defaultValue(1).min(0).sliderMax(25).build());
    // 最大骨粉使用次数设置，控制对单个树苗最多使用多少次骨粉
    private final Setting<Integer> maxBonemealAttempts = sgGeneral.add(new IntSetting.Builder()
        .name("最大骨粉次数").description("对单个树苗最多使用骨粉的次数").defaultValue(20).min(1).sliderMax(200).build());
    // 水平种植半径设置，控制水平方向上的种植范围
    private final Setting<Integer> rRange = sgGeneral.add(new IntSetting.Builder()
        .name("半径").description("您可以水平放置多远").defaultValue(4).min(1).sliderMax(5).build());
    // 垂直种植范围设置，控制垂直方向上的种植范围
    private final Setting<Integer> yRange = sgGeneral.add(new IntSetting.Builder()
        .name("Y轴范围").description("您可以垂直放置多远").defaultValue(3).min(1).sliderMax(5).build());
    // 排序模式设置（最近或最远），控制选择种植/施肥位置的优先级
    private final Setting<SortMode> sortMode = sgGeneral.add(new EnumSetting.Builder<SortMode>()
        .name("排序模式").description("如何排序附近的树木/放置位置").defaultValue(SortMode.Farthest).build());

    // 骨粉使用计时器和种植计时器，用于控制操作频率
    private int bonemealTimer, plantTimer;
    // 跟踪每个树苗位置的骨粉使用次数
    private Map<BlockPos, Integer> saplingBonemealCount;


    public TreeAura() { // CopeTypes
        super("自动种树", "在你周围种树！催熟");
    }

    @Override
    public void onActivate() {
        // 激活模块时重置计时器和状态
        bonemealTimer = 0;
        plantTimer = 0;
        saplingBonemealCount = new HashMap<>();
    }

    @EventHandler
    public void onTick(TickEvent.Post event) {
        // 每个游戏刻减少计时器
        plantTimer--;
        bonemealTimer--;


        // 当骨粉计时器归零时处理骨粉催熟逻辑
        if (bonemealTimer <= 0) {
            processBonemealForAllSaplings();
            bonemealTimer = bonemealDelay.get();
        }

        // 当种植计时器归零时寻找种植位置并种植树苗
        if (plantTimer <= 0) {
            BlockPos plantPos = findPlantLocation();
            if (plantPos != null) {
                doPlant(plantPos);
            }
            plantTimer = plantDelay.get();
        }
    }


    // 查找背包中的骨粉
    private FindItemResult findBonemeal() {
        return InvUtils.findInHotbar(Items.BONE_MEAL);
    }

    // 查找背包中的树苗
    private FindItemResult findSapling() {
        return InvUtils.findInHotbar(itemStack -> Block.getBlockFromItem(itemStack.getItem()) instanceof SaplingBlock);
    }

    // 判断指定位置是否为树苗
    private boolean isSapling(BlockPos pos) {
        return MinecraftClient.getInstance().world.getBlockState(pos).getBlock() instanceof SaplingBlock;
    }

    // 判断指定位置是否适合生长
    // 判断指定位置是否适合树木生长（树叶、原木、空气都不会阻止生长）
    private boolean isGrowthFriendlyBlock(BlockPos pos) {
        Block block = MinecraftClient.getInstance().world.getBlockState(pos).getBlock();


        // 空气、树叶、原木、藤蔓等不会阻止树木生长
        return block instanceof AirBlock ||
            block instanceof LeavesBlock ||
            block instanceof PillarBlock ||  // 原木类方块
            block.getDefaultState().isIn(BlockTags.LOGS) ||
            block.getDefaultState().isIn(BlockTags.LEAVES) ||
            block instanceof VineBlock ||    // 藤蔓
            block instanceof PlantBlock ||   // 植物类方块（草、花等）
            block.equals(Blocks.SNOW) ||     // 雪
            block.equals(Blocks.TALL_GRASS) ||
            block.equals(Blocks.SHORT_GRASS) ||
            block.equals(Blocks.FERN) ||
            block.equals(Blocks.LARGE_FERN);
    }

    // 在指定位置种植树苗
    private void doPlant(BlockPos plantPos) {
        FindItemResult sapling = findSapling();
        if (!sapling.found()) {
            error("快捷栏中没有树苗");
            return;
        }

        InvUtils.swap(sapling.slot(), false);
        // 使用 BlockUtilGrim.clickBlock 方法，会自动处理角度
        BlockUtilGrim.clickBlock(plantPos, Direction.UP, true, Hand.MAIN_HAND, BlockUtilGrim.SwingSide.All);
    }

    // 处理范围内所有树苗的骨粉催熟逻辑
    private void processBonemealForAllSaplings() {
        // 清理已经不是树苗的位置记录
        cleanupSaplingRecords();

        // 获取范围内所有树苗
        List<BlockPos> saplings = findAllSaplingsInRange();
        if (saplings.isEmpty()) return;

        // 找到第一个还没达到最大骨粉次数的树苗
        for (BlockPos sapling : saplings) {
            int currentCount = saplingBonemealCount.getOrDefault(sapling, 0);
            if (currentCount < maxBonemealAttempts.get()) {
                // 对这个树苗使用骨粉
                doBonemeal(sapling);
                // 更新使用次数
                saplingBonemealCount.put(sapling, currentCount + 1);
                // 每次只处理一个树苗，然后等待下一个tick
                return;
            } else {
                info("树苗 " + sapling + " 已达到最大使用次数，跳过");
            }
        }
    }

    // 清理已经不是树苗的位置记录
    private void cleanupSaplingRecords() {
        saplingBonemealCount.entrySet().removeIf(entry -> !isSapling(entry.getKey()));
    }

    // 获取范围内所有树苗
    private List<BlockPos> findAllSaplingsInRange() {
        return findSaplings(MinecraftClient.getInstance().player.getBlockPos(), rRange.get(), yRange.get());
    }

    // 对指定位置的树苗施加骨粉
    private void doBonemeal(BlockPos sapling) {
        FindItemResult bonemeal = findBonemeal();
        if (!bonemeal.found()) {
            error("快捷栏中没有骨粉");
            return;
        }
        InvUtils.swap(bonemeal.slot(), false);
        // 使用 BlockUtilGrim.clickBlock 方法，会自动处理角度
        BlockUtilGrim.clickBlock(sapling, Direction.UP, true, Hand.MAIN_HAND, BlockUtilGrim.SwingSide.All);
    }

    // 判断指定位置是否可以种植树苗
    private boolean canPlant(BlockPos pos) {
        Block b = MinecraftClient.getInstance().world.getBlockState(pos).getBlock();
        // 只能在草方块、泥土等允许种植的地方种植
        if (b.equals(Blocks.SHORT_GRASS) || b.equals(Blocks.GRASS_BLOCK) || b.equals(Blocks.DIRT) || b.equals(Blocks.COARSE_DIRT)) {
            final AtomicBoolean plant = new AtomicBoolean(true);
            // 检查上方5格内是否有障碍物，确保树木有足够的生长空间
            IntStream.rangeClosed(1, 5).forEach(i -> {
                // 检查正上方 - 必须是空气（树干生长位置）
                BlockPos check = pos.up(i);
                if (!MinecraftClient.getInstance().world.getBlockState(check).getBlock().equals(Blocks.AIR)) {
                    plant.set(false);
                    return;
                }
                // 检查四周 - 使用更宽松的生长友好检查（树叶、原木等可以存在）
                for (CardinalDirection dir : CardinalDirection.values()) {
                    BlockPos sidePos = check.offset(dir.toDirection(), 1);
                    if (!isGrowthFriendlyBlock(sidePos)) {
                        plant.set(false);
                        return;
                    }
                }
            });
            return plant.get();
        }
        return false;
    }

    // 查找指定范围内的所有树苗
    private List<BlockPos> findSaplings(BlockPos centerPos, int radius, int height) {
        ArrayList<BlockPos> blocc = new ArrayList<>();
        List<BlockPos> blocks = WorldUtils.getSphere(centerPos, radius, height);
        for (BlockPos b : blocks) if (isSapling(b)) blocc.add(b);
        return blocc;
    }


    // 获取可以种植树苗的位置列表
    private List<BlockPos> getPlantLocations(BlockPos centerPos, int radius, int height) {
        ArrayList<BlockPos> blocc = new ArrayList<>();
        List<BlockPos> blocks = WorldUtils.getSphere(centerPos, radius, height);
        for (BlockPos b : blocks) if (canPlant(b)) blocc.add(b);
        return blocc;
    }

    // 查找合适的种植位置
    private BlockPos findPlantLocation() {
        List<BlockPos> nearby = getPlantLocations(MinecraftClient.getInstance().player.getBlockPos(), rRange.get(), yRange.get());
        if (nearby.isEmpty()) return null;
        // 根据距离排序
        nearby.sort(Comparator.comparingDouble(PlayerUtils::distanceTo));
        // 根据排序模式决定使用最近还是最远的位置
        if (sortMode.get().equals(SortMode.Farthest)) Collections.reverse(nearby);
        return nearby.get(0);
    }

    // 计算两个位置之间的距离
    private double distanceBetween(BlockPos pos1, BlockPos pos2) {
        double d = pos1.getX() - pos2.getX();
        double e = pos1.getY() - pos2.getY();
        double f = pos1.getZ() - pos2.getZ();
        return MathHelper.sqrt((float) (d * d + e * e + f * f));
    }

    // 排序模式枚举：最近或最远
    public enum SortMode {
        // 最近优先模式
        Closest("最近优先"),
        // 最远优先模式
        Farthest("最远优先");

        private final String displayName;

        SortMode(String displayName) {
            this.displayName = displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }

}
