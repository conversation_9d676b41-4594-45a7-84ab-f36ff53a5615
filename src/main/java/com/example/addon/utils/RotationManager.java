package com.example.addon.utils;

import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.utils.player.Rotations;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;


public class RotationManager {
    MinecraftClient mc = MinecraftClient.getInstance();

    // 单例实例，使用 volatile 确保多线程环境下的可见性
    private static volatile RotationManager instance;

    public Rotation currentRotation = null;
    Timer timer = new Timer();

    // 私有构造函数，防止外部直接实例化
    private RotationManager() {
        MeteorClient.EVENT_BUS.subscribe(this);
        mc = MinecraftClient.getInstance();

    }

    /**
     * 获取 RotationManager 的单例实例
     * 使用双重检查锁定模式实现线程安全的懒加载
     *
     * @return RotationManager 的唯一实例
     */
    public static RotationManager getInstance() {
        if (instance == null) {
            synchronized (RotationManager.class) {
                if (instance == null) {
                    instance = new RotationManager();
                }
            }
        }
        return instance;
    }

    public boolean register(Rotation rotation) {
        if (this.currentRotation != null && this.currentRotation.getPriority() > rotation.getPriority()) {
            return false;
        } else {
            this.currentRotation = rotation;
            this.timer.reset();
            mc.player
                .networkHandler
                .sendPacket(
                    new Full(
                        mc.player.getX(),
                        mc.player.getY(),
                        mc.player.getZ(),
                        rotation.getYaw(),
                        rotation.getPitch(),
                        mc.player.isOnGround()
                    )
                );
            Rotations.setCamRotation(rotation.getYaw(), rotation.getPitch());
            return true;
        }
    }

    public void sync() {
        mc.player
            .networkHandler
            .sendPacket(
                new Full(
                    mc.player.getX(),
                    mc.player.getY(),
                    mc.player.getZ(),
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    mc.player.isOnGround()
                )
            );
        this.currentRotation = null;
    }
}
